#-*- coding: utf-8 -*-
import os, sys, time, datetime, random, json, struct
import cPickle as pickle
from common import utils

from jsonrpc.proxy import ServiceProxy
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../', 'trunk/llol/src'))
#sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../', 'trunk/game_lib'))
#from game_lib.models.config import Configs
import settings
import urllib2
from sqlalchemy import create_engine

import zlib
from sqlalchemy import text
import socket






DB_ENCODING = 'utf-8'
DB_ECHO = False
DBENGINE = 'mysql://%s:%s@%s:%s/%s?charset=utf8' % settings.DB_ACCOUNT_PASSWORD['shards']['1']['master']
# 优化备份服务数据库连接 - 兼容MySQL 5.6.5
MasterDB = create_engine(
    DBENGINE,
    echo=DB_ECHO,
    encoding=DB_ENCODING,
    pool_size=8,            # 备份服务连接数
    max_overflow=15,        # 最大溢出连接
    pool_recycle=3600,      # 连接回收时间
    pool_timeout=60,        # 备份操作超时时间更长
    connect_args={
        'connect_timeout': 15,
        'charset': 'utf8',
    }
)

conn = MasterDB.connect()
_wapi = ServiceProxy(settings.BASE2_URL[0])



zone_config = None

def call_server_api(zone, method, params):
    data = [method, params]
    url = ('https://' if settings.USE_SSL else 'http://') + ':'.join(map(str, zone_config[zone][1])) + '/api/?pwd=' + settings.PWD
    print "Request URL:", url  # 仅添加这一行调试输出
    
    if settings.USE_SSL:
        return urllib2.urlopen(url, pickle.dumps(data), timeout=600).read()
    else:
        return urllib2.urlopen(url, pickle.dumps(data), timeout=600).read()
def get_server_config_version(zone):
    res = call_server_api(zone, 'get_server_status', {})
    res = pickle.loads(res)
    return res[0]


def user_backup(zone, if_all=0, if_honour_done=0):
    res = call_server_api(zone, 'backup_user', {'if_all':if_all,'if_honour_done': if_honour_done})
    print '********************user_total-%s====%s' % (zone,res)
    return False

def fight_log_backup(zone, if_all=0):
    res = call_server_api(zone, 'backup_fight_log', {'if_all':if_all})
    print '********************fight_log_total-%s====%s' % (zone,res)
    return False

def world_backup(zone):
    res = call_server_api(zone, 'backup_world', {})
    return False

def get_zone_list():
    global zone_config
    zone_config,config_version =  _wapi.get_zone_config_and_version()['result']
    return sorted(zone_config.items(), key=lambda x:x[1][2], reverse=True),config_version


def get_duplicate_server_config():
    duplicate_server_config =  _wapi.get_duplicate_server_config()['result']
    return duplicate_server_config




if __name__ == '__main__':
    import datetime
    if settings.WHERE == 'local':
        backup_seconds = 60
    else:
        backup_seconds = 60
    zone_ip_dict = {}

    local_ip = urllib2.urlopen(settings.BASE_URL+'/ip/').read()
    backup_all_hour = None
    runing_server_zone_list = []
    while 1:
        print runing_server_zone_list
        now = datetime.datetime.now()
        for zone,v in get_zone_list():
            try:
                if not zone_ip_dict.has_key(v[1][0]):
                    zone_ip_dict[v[1][0]] = getIP(v[1][0])
                zone_ip = zone_ip_dict[v[1][0]]
                if local_ip != zone_ip:
                    continue
                t = time.time()
                if now.hour != backup_all_hour:
                    print '------------backup----user_all',zone
                    user_backup(zone, 1)
                    print '------------backup----fight_log_all',zone
                    fight_log_backup(zone, 1)
                    print '------------backup----pk_robot',zone
                    pk_robot_backup(zone)
                else:
                    print '------------backup----user',zone
                    user_backup(zone, 0)
                    print '------------backup----fight_log',zone
                    fight_log_backup(zone, 0)

                print '------------backup----cuntry_club',zone
                country_club_backup(zone)
                print '------------backup----system_data',zone
                system_data_backup(zone)
                print '------------backup----world',zone
                world_backup(zone)
                print time.time()-t
                if zone not in runing_server_zone_list:
                    runing_server_zone_list.append(zone)
            except Exception,e:
                print e
                if zone not in runing_server_zone_list:
                    if now + datetime.timedelta(minutes=10) > v[2]:
                        os.system('python server.py --zone=%s --log-file-prefix=/data/logs/log%s >-dev-null 2>&1 &' % (zone,zone))
                        print 'start----serve----zone----',zone
                        runing_server_zone_list.append(zone)
        backup_all_hour = now.hour
        time.sleep(backup_seconds)










