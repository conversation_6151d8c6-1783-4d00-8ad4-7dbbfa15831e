# 跨服赛报名500错误修复说明

## 问题描述
用户在跨服赛报名时遇到"500 list index out of range"错误。

## 问题根源分析

### 1. 主要问题：User.duplicate_time[1]为None时的索引越界
**位置：** `server/service/server.py` 第9435行（修复前）
**原因：** 
- `User.duplicate_time`的结构是：`[season_num, [join_start_time, join_end_time]]` 或者 `[season_num, None]`
- 当跨服赛没有开启或配置有问题时，`User.duplicate_time[1]`会是`None`
- 尝试解包`join_start_time,join_end_time = User.duplicate_time[1]`时导致错误

**修复方案：**
```python
# 检查跨服赛时间配置是否有效
if not User.duplicate_time or User.duplicate_time[1] is None:
    raise Model_Error(10595, u'跨服战尚未开启或时间配置无效')
join_start_time,join_end_time = User.duplicate_time[1]
```

### 2. 类似问题：get_duplicate方法中的相同错误
**位置：** `server/service/server.py` 第9309行（修复前）
**修复方案：** 添加相同的检查逻辑

### 3. 禁赛时间设置中的潜在问题
**位置：** `server/service/server.py` 第42290-42302行（修复前）
**修复方案：** 添加安全检查，避免在`User.duplicate_time[1]`为None时访问

### 4. 段位配置索引越界问题
**位置：** `server/trunk/admin/admin/views/app_user.py` 多处
**原因：** 直接访问`game_config.duplicate['level'][level][5]`等，当level超出配置范围时导致索引越界
**修复方案：** 添加边界检查和异常处理

## 修复的文件

### 1. server/service/server.py
- 修复`admin_join_duplicate`方法中的索引越界问题
- 修复`get_duplicate`方法中的索引越界问题  
- 修复禁赛时间设置中的潜在问题

### 2. server/trunk/admin/admin/views/app_user.py
- 修复`get_duplicate_join_user`函数中的段位配置访问
- 修复`view_duplicate`函数中多处段位配置访问

## 触发条件
这些错误通常在以下情况下发生：
1. 跨服赛功能未开启（switch=0）
2. 跨服赛时间配置有问题
3. `wapi.get_duplicate_open_date(now)`返回None
4. 段位参数超出配置范围
5. 服务器重启后跨服赛时间未正确初始化

## 预防措施
1. 在所有访问`User.duplicate_time[1]`的地方添加None检查
2. 在访问配置数组时添加边界检查
3. 使用try-catch包装可能出错的配置访问
4. 提供合理的默认值和错误提示

## 测试建议
1. 测试跨服赛功能关闭时的报名操作
2. 测试无效段位参数的处理
3. 测试服务器重启后的跨服赛状态
4. 测试配置文件异常时的错误处理
