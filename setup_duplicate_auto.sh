#!/bin/bash
# 跨服战自动化管理一键安装脚本
# 使用方法：chmod +x setup_duplicate_auto.sh && ./setup_duplicate_auto.sh

echo "=========================================="
echo "    跨服战自动化管理 - 一键安装程序"
echo "=========================================="
echo "本程序将为您自动设置跨服战服务器管理"
echo "安装完成后将永久自动管理，无需手动干预"
echo ""

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    echo "⚠️  检测到您是root用户，建议使用普通用户运行"
    read -p "是否继续？(y/n): " continue_root
    if [ "$continue_root" != "y" ]; then
        echo "安装已取消"
        exit 1
    fi
fi

echo "🔧 正在创建必要目录..."
mkdir -p /data/logs
mkdir -p /data/server/service

echo "📝 正在创建跨服战自动化管理脚本..."
cat > /data/server/service/duplicate_auto.sh << 'SCRIPT_EOF'
#!/bin/bash
# 跨服战自动化管理脚本 - 由系统定时执行

LOG_FILE="/data/logs/duplicate_auto.log"
SERVICE_DIR="/data/server/service"

# 日志记录函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# 检查进程是否运行
is_running() {
    pgrep -f "$1" > /dev/null
}

# 启动跨服战管理服务器
start_manage_server() {
    if ! is_running "duplicate_manage.py"; then
        cd "$SERVICE_DIR"
        nohup python duplicate_manage.py --mnum=0 --log-file-prefix=/data/logs/dupmanage > /dev/null 2>&1 &
        log_message "✅ 跨服战管理服务器已启动 (PID: $!)"
        echo "跨服战管理服务器已启动"
    else
        log_message "ℹ️  跨服战管理服务器已在运行"
    fi
}

# 启动跨服战战斗服务器
start_battle_server() {
    if ! is_running "duplicate_server.py"; then
        cd "$SERVICE_DIR"
        nohup python duplicate_server.py --dnum=0 --log-file-prefix=/data/logs/dup0 > /dev/null 2>&1 &
        log_message "⚔️  跨服战战斗服务器已启动 (PID: $!)"
        echo "跨服战战斗服务器已启动"
    else
        log_message "ℹ️  跨服战战斗服务器已在运行"
    fi
}

# 停止跨服战管理服务器
stop_manage_server() {
    if is_running "duplicate_manage.py"; then
        pkill -f duplicate_manage.py
        log_message "🛑 跨服战管理服务器已停止"
        echo "跨服战管理服务器已停止"
    fi
}

# 停止跨服战战斗服务器
stop_battle_server() {
    if is_running "duplicate_server.py"; then
        pkill -f duplicate_server.py
        log_message "🛑 跨服战战斗服务器已停止"
        echo "跨服战战斗服务器已停止"
    fi
}

# 获取当前时间
CURRENT_DAY=$(date +%u)  # 1=周一, 3=周三, 6=周六
CURRENT_HOUR=$(date +%H)

# 记录检查日志（仅在关键时间点记录，避免日志过多）
case "$CURRENT_DAY-$CURRENT_HOUR" in
    "1-09"|"3-17"|"3-23"|"4-09"|"6-17"|"7-01")
        log_message "🔍 定时检查 - 星期$CURRENT_DAY, ${CURRENT_HOUR}点"
        ;;
esac

# 时间控制逻辑 - 根据实际需要调整这些时间
case "$CURRENT_DAY-$CURRENT_HOUR" in
    "1-09")  # 周一 09:00 - 为周三跨服战做准备
        log_message "📅 周三跨服战准备：启动管理服务器"
        start_manage_server
        ;;
    "3-17")  # 周三 17:00 - 战斗前1小时启动战斗服务器
        log_message "⚔️  周三跨服战即将开始：启动战斗服务器"
        start_battle_server
        ;;
    "3-23")  # 周三 23:00 - 战斗结束，停止战斗服务器
        log_message "🏁 周三跨服战结束：停止战斗服务器"
        stop_battle_server
        ;;
    "4-09")  # 周四 09:00 - 为周六跨服战做准备，重启管理服务器
        log_message "📅 周六跨服战准备：重启管理服务器"
        stop_manage_server
        sleep 3
        start_manage_server
        ;;
    "6-17")  # 周六 17:00 - 战斗前1小时启动战斗服务器
        log_message "⚔️  周六跨服战即将开始：启动战斗服务器"
        start_battle_server
        ;;
    "7-01")  # 周日 01:00 - 本周跨服战全部结束
        log_message "🏁 本周跨服战全部结束：停止所有服务器"
        stop_battle_server
        stop_manage_server
        ;;
esac
SCRIPT_EOF

echo "📊 正在创建状态查看脚本..."
cat > /data/server/service/duplicate_status.sh << 'STATUS_EOF'
#!/bin/bash
# 跨服战状态查看脚本

echo "=========================================="
echo "        跨服战服务器状态监控"
echo "=========================================="
echo ""

echo "📊 当前时间：$(date '+%Y-%m-%d %H:%M:%S %A')"
echo ""

echo "🔧 服务器运行状态："
if pgrep -f "duplicate_manage.py" > /dev/null; then
    manage_pid=$(pgrep -f duplicate_manage.py)
    echo "  ✅ 跨服战管理服务器：运行中 (PID: $manage_pid)"
else
    echo "  ❌ 跨服战管理服务器：未运行"
fi

if pgrep -f "duplicate_server.py" > /dev/null; then
    server_pid=$(pgrep -f duplicate_server.py)
    echo "  ✅ 跨服战战斗服务器：运行中 (PID: $server_pid)"
else
    echo "  ❌ 跨服战战斗服务器：未运行"
fi

echo ""
echo "📋 端口监听状态："
netstat_output=$(netstat -tlnp 2>/dev/null | grep -E "(9901|10000)")
if [ -n "$netstat_output" ]; then
    echo "$netstat_output" | while read line; do
        echo "  📡 $line"
    done
else
    echo "  📡 未检测到跨服战相关端口监听"
fi

echo ""
echo "📝 最近10条自动化日志："
if [ -f "/data/logs/duplicate_auto.log" ]; then
    tail -10 /data/logs/duplicate_auto.log | sed 's/^/  /'
else
    echo "  📝 暂无自动化日志文件"
fi

echo ""
echo "⏰ 定时任务状态："
if crontab -l 2>/dev/null | grep -q "duplicate_auto.sh"; then
    echo "  ✅ 自动化定时任务已设置"
    crontab -l 2>/dev/null | grep "duplicate_auto.sh" | sed 's/^/  ⏰ /'
else
    echo "  ❌ 自动化定时任务未设置"
fi

echo ""
echo "📅 下次自动操作时间预测："
current_day=$(date +%u)
current_hour=$(date +%H)

case $current_day in
    1) # 周一
        if [ $current_hour -lt 9 ]; then
            echo "  🕘 今天 09:00 - 启动管理服务器（周三准备）"
        else
            echo "  🕘 周三 17:00 - 启动战斗服务器"
        fi
        ;;
    2) # 周二
        echo "  🕘 周三 17:00 - 启动战斗服务器"
        ;;
    3) # 周三
        if [ $current_hour -lt 17 ]; then
            echo "  🕘 今天 17:00 - 启动战斗服务器"
        elif [ $current_hour -lt 23 ]; then
            echo "  🕘 今天 23:00 - 停止战斗服务器"
        else
            echo "  🕘 周四 09:00 - 重启管理服务器（周六准备）"
        fi
        ;;
    4) # 周四
        if [ $current_hour -lt 9 ]; then
            echo "  🕘 今天 09:00 - 重启管理服务器（周六准备）"
        else
            echo "  🕘 周六 17:00 - 启动战斗服务器"
        fi
        ;;
    5) # 周五
        echo "  🕘 周六 17:00 - 启动战斗服务器"
        ;;
    6) # 周六
        if [ $current_hour -lt 17 ]; then
            echo "  🕘 今天 17:00 - 启动战斗服务器"
        else
            echo "  🕘 周日 01:00 - 停止所有服务器"
        fi
        ;;
    7) # 周日
        if [ $current_hour -lt 1 ]; then
            echo "  🕘 今天 01:00 - 停止所有服务器"
        else
            echo "  🕘 周一 09:00 - 启动管理服务器（周三准备）"
        fi
        ;;
esac

echo ""
echo "=========================================="
STATUS_EOF

echo "🎮 正在创建手动控制脚本..."
cat > /data/server/service/duplicate_control.sh << 'CONTROL_EOF'
#!/bin/bash
# 跨服战手动控制脚本

LOG_FILE="/data/logs/duplicate_manual.log"

log_action() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - [手动操作] $1" >> "$LOG_FILE"
}

case "$1" in
    "start")
        echo "🚀 手动启动所有跨服战服务器..."
        cd /data/server/service
        
        # 启动管理服务器
        if ! pgrep -f "duplicate_manage.py" > /dev/null; then
            nohup python duplicate_manage.py --mnum=0 --log-file-prefix=/data/logs/dupmanage > /dev/null 2>&1 &
            echo "✅ 跨服战管理服务器已启动 (PID: $!)"
            log_action "启动跨服战管理服务器 (PID: $!)"
        else
            echo "ℹ️  跨服战管理服务器已在运行"
        fi
        
        # 启动战斗服务器
        if ! pgrep -f "duplicate_server.py" > /dev/null; then
            nohup python duplicate_server.py --dnum=0 --log-file-prefix=/data/logs/dup0 > /dev/null 2>&1 &
            echo "✅ 跨服战战斗服务器已启动 (PID: $!)"
            log_action "启动跨服战战斗服务器 (PID: $!)"
        else
            echo "ℹ️  跨服战战斗服务器已在运行"
        fi
        
        echo "🎉 启动完成！"
        ;;
    "stop")
        echo "🛑 手动停止所有跨服战服务器..."
        
        if pgrep -f "duplicate_manage.py" > /dev/null; then
            pkill -f duplicate_manage.py
            echo "✅ 跨服战管理服务器已停止"
            log_action "停止跨服战管理服务器"
        else
            echo "ℹ️  跨服战管理服务器未在运行"
        fi
        
        if pgrep -f "duplicate_server.py" > /dev/null; then
            pkill -f duplicate_server.py
            echo "✅ 跨服战战斗服务器已停止"
            log_action "停止跨服战战斗服务器"
        else
            echo "ℹ️  跨服战战斗服务器未在运行"
        fi
        
        echo "🎉 停止完成！"
        ;;
    "restart")
        echo "🔄 重启所有跨服战服务器..."
        
        # 先停止
        pkill -f duplicate_manage.py 2>/dev/null
        pkill -f duplicate_server.py 2>/dev/null
        echo "⏳ 等待进程完全停止..."
        sleep 5
        
        # 再启动
        cd /data/server/service
        nohup python duplicate_manage.py --mnum=0 --log-file-prefix=/data/logs/dupmanage > /dev/null 2>&1 &
        manage_pid=$!
        nohup python duplicate_server.py --dnum=0 --log-file-prefix=/data/logs/dup0 > /dev/null 2>&1 &
        server_pid=$!
        
        echo "✅ 跨服战管理服务器已重启 (PID: $manage_pid)"
        echo "✅ 跨服战战斗服务器已重启 (PID: $server_pid)"
        log_action "重启所有跨服战服务器 (管理PID: $manage_pid, 战斗PID: $server_pid)"
        
        echo "🎉 重启完成！"
        ;;
    "status")
        /data/server/service/duplicate_status.sh
        ;;
    *)
        echo "=========================================="
        echo "        跨服战手动控制工具"
        echo "=========================================="
        echo ""
        echo "用法: $0 {start|stop|restart|status}"
        echo ""
        echo "命令说明："
        echo "  start   - 立即启动所有跨服战服务器"
        echo "  stop    - 立即停止所有跨服战服务器"
        echo "  restart - 重启所有跨服战服务器"
        echo "  status  - 查看当前状态"
        echo ""
        echo "示例："
        echo "  $0 start    # 启动服务器"
        echo "  $0 status   # 查看状态"
        echo ""
        ;;
esac
CONTROL_EOF

# 设置所有脚本的执行权限
chmod +x /data/server/service/duplicate_auto.sh
chmod +x /data/server/service/duplicate_status.sh
chmod +x /data/server/service/duplicate_control.sh

echo "⏰ 正在设置定时任务..."

# 备份现有的crontab
crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || echo "# 新的crontab" > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S)

# 检查是否已经存在相同的定时任务
if crontab -l 2>/dev/null | grep -q "duplicate_auto.sh"; then
    echo "⚠️  检测到已存在跨服战自动化任务，正在更新..."
    # 移除旧的任务
    crontab -l 2>/dev/null | grep -v "duplicate_auto.sh" | crontab -
fi

# 添加新的定时任务
(crontab -l 2>/dev/null; echo "# 跨服战自动化管理 - 每小时检查一次") | crontab -
(crontab -l 2>/dev/null; echo "0 * * * * /data/server/service/duplicate_auto.sh >/dev/null 2>&1") | crontab -

echo ""
echo "🎉 跨服战自动化管理安装完成！"
echo ""
echo "=========================================="
echo "           安装成功 - 使用说明"
echo "=========================================="
echo ""
echo "📋 自动化时间表："
echo "  • 周一 09:00 - 自动启动管理服务器（为周三做准备）"
echo "  • 周三 17:00 - 自动启动战斗服务器"
echo "  • 周三 23:00 - 自动停止战斗服务器"
echo "  • 周四 09:00 - 重启管理服务器（为周六做准备）"
echo "  • 周六 17:00 - 自动启动战斗服务器"
echo "  • 周日 01:00 - 自动停止所有服务器"
echo ""
echo "🛠️  常用管理命令："
echo "  查看状态: /data/server/service/duplicate_status.sh"
echo "  手动启动: /data/server/service/duplicate_control.sh start"
echo "  手动停止: /data/server/service/duplicate_control.sh stop"
echo "  重启服务: /data/server/service/duplicate_control.sh restart"
echo ""
echo "📝 日志文件位置："
echo "  自动化日志: /data/logs/duplicate_auto.log"
echo "  手动操作日志: /data/logs/duplicate_manual.log"
echo ""
echo "✅ 现在您可以完全不用管了！"
echo "   系统会自动在正确的时间启动/停止跨服战服务器"
echo ""
echo "💡 建议："
echo "   1. 偶尔运行 duplicate_status.sh 检查状态"
echo "   2. 如需修改时间，编辑 /data/server/service/duplicate_auto.sh"
echo "   3. 紧急情况可使用 duplicate_control.sh 手动控制"
echo ""
echo "=========================================="

# 显示当前状态
echo ""
echo "📊 当前状态检查："
/data/server/service/duplicate_status.sh

echo ""
echo "🎯 安装完成！您现在可以关闭终端，系统会自动管理一切。"
