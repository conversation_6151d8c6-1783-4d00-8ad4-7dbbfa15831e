#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
跨服战报名修复测试脚本
用于测试跨服战报名功能是否正常工作
"""

import os
import sys
import requests
import json

def test_admin_join_duplicate():
    """测试管理员跨服战报名功能"""
    
    # 测试参数
    test_params = {
        'uid': '10001',  # 测试用户ID
        'zone': '5',     # 测试区服
        'level': '0'     # 测试段位（青铜）
    }
    
    # 后台管理地址（根据实际情况修改）
    admin_url = 'http://localhost:8500/admin/app_user/admin_join_duplicate/'
    
    print("开始测试跨服战报名功能...")
    print("测试参数:", test_params)
    
    try:
        # 发送POST请求
        response = requests.post(admin_url, data=test_params, timeout=30)
        
        print("HTTP状态码:", response.status_code)
        print("响应内容:", response.text)
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('state') == 'success':
                    print("✓ 跨服战报名成功！")
                    return True
                else:
                    print("✗ 跨服战报名失败:", result.get('msg', '未知错误'))
                    return False
            except ValueError:
                print("✗ 响应不是有效的JSON格式")
                return False
        else:
            print("✗ HTTP请求失败，状态码:", response.status_code)
            return False
            
    except requests.exceptions.RequestException as e:
        print("✗ 请求异常:", str(e))
        return False

def check_log_files():
    """检查日志文件"""
    log_files = [
        'server/service/app.log',
        'server/trunk/admin/admin/views/admin_app.log'
    ]
    
    print("\n检查日志文件:")
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"✓ 日志文件存在: {log_file}")
            # 显示最后10行日志
            try:
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    if lines:
                        print(f"  最后几行日志:")
                        for line in lines[-5:]:
                            print(f"    {line.strip()}")
                    else:
                        print(f"  日志文件为空")
            except Exception as e:
                print(f"  读取日志文件失败: {e}")
        else:
            print(f"✗ 日志文件不存在: {log_file}")

def main():
    """主函数"""
    print("=" * 50)
    print("跨服战报名修复测试")
    print("=" * 50)
    
    # 检查日志文件
    check_log_files()
    
    print("\n" + "=" * 50)
    print("开始功能测试")
    print("=" * 50)
    
    # 测试报名功能
    success = test_admin_join_duplicate()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    
    if success:
        print("✓ 所有测试通过！跨服战报名功能正常。")
    else:
        print("✗ 测试失败！请检查日志文件获取详细错误信息。")
        print("\n建议检查以下内容:")
        print("1. 服务器是否正常运行")
        print("2. 数据库连接是否正常")
        print("3. 跨服战配置是否正确")
        print("4. 用户数据是否存在")
    
    return success

if __name__ == '__main__':
    main()
