#!/bin/bash

# 日志目录
LOG_DIR="/data/server/logs"
mkdir -p "$LOG_DIR"

# 函数：检查进程是否已运行
is_running() {
    pgrep -f "$1" >/dev/null 2>&1
}

# 函数：启动服务
start_service() {
    local name="$1"
    local cmd="$2"
    local log_file="$LOG_DIR/${name}.log"

    echo "[$(date)] 正在检查 [$cmd]" | tee -a "$log_file"

    if is_running "$cmd"; then
        echo "[$(date)] $name 已在运行，跳过启动。" | tee -a "$log_file"
    else
        echo "[$(date)] 启动 $name ..." | tee -a "$log_file"
        nohup $cmd >> "$log_file" 2>&1 &
        echo "[$(date)] $name 启动完成！PID: $!" | tee -a "$log_file"
    fi
}

# 函数：停止服务
stop_service() {
    local name="$1"
    local cmd="$2"
    local log_file="$LOG_DIR/${name}.log"

    echo "[$(date)] 正在停止 $name ..." | tee -a "$log_file"

    # 查找并杀死匹配的进程
    PIDS=$(pgrep -f "$cmd")
    if [ -n "$PIDS" ]; then
        echo "[$(date)] 找到以下 PID: $PIDS，正在终止..." | tee -a "$log_file"
        echo "$PIDS" | xargs kill -9 2>/dev/null
        echo "[$(date)] $name 已停止。" | tee -a "$log_file"
    else
        echo "[$(date)] 没有找到正在运行的 $name 进程。" | tee -a "$log_file"
    fi
}

# 主流程
ACTION="$1"

if [ "$ACTION" == "start" ]; then

    echo "========== $(date) 开始启动所有服务 =========="

    # 1. 启动统计服务
    start_service "Python_Analytics_7701" "python /data/server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7701"

    # 2. 启动后台服务
    start_service "Python_LLOL_8500" "python /data/server/trunk/llol/src/manage.py runserver 0.0.0.0:8500"

    # 3. 启动战斗服
    (
        cd /data/server/service || { echo "无法进入目录 /data/server/service"; exit 1; }
        start_service "Node_Laya_3001" "node Laya.js 3001"
    )

    # 4. 启动游戏区服
    (
        cd /data/server/service || { echo "无法进入目录 /data/server/service"; exit 1; }
        start_service "Python_Backup_SG3" "python backup_start_sg3.py"
    )
    # 5. 启动1区服务
    (
        cd /data/server/service || { echo "无法进入目录 /data/server/service"; exit 1; }
        start_service "Python_Zone_5001" "python server.py --zone=1"
    )

    # 6. 启动2区服务
    (
        cd /data/server/service || { echo "无法进入目录 /data/server/service"; exit 1; }
        start_service "Python_Zone_5002" "python server.py --zone=2"
    )

    echo "========== $(date) 所有服务启动完成 =========="

elif [ "$ACTION" == "stop" ]; then

    echo "========== $(date) 开始停止所有服务 =========="

    # 1. 停止统计服务
    stop_service "Python_Analytics_7701" "python /data/server/analytics_sg3/trunk/src/manage.py runserver"

    # 2. 停止后台服务
    stop_service "Python_LLOL_8500" "python /data/server/trunk/llol/src/manage.py runserver"

    # 3. 停止战斗服
    stop_service "Node_Laya_3001" "node Laya.js 3001"

    # 4. 停止区服服务
    stop_service "Python_Backup_SG3" "python backup_start_sg3.py"
    
    # 5. 停止一区服务
    stop_service "Python_Zone_5001" "python server.py --zone=1"

    # 6. 停止2区服务
    stop_service "Python_Zone_5002" "python server.py --zone=2"
    
    echo "========== $(date) 所有服务已停止 =========="

else
    echo "用法: $0 [start|stop]"
    exit 1
fi