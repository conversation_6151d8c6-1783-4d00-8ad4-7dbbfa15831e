#-*- coding: utf-8 -*-
import redis
import zlib
import c<PERSON>ickle as pickle
from django.conf import settings


class RedisCache(object):
    '''
    cache
    '''
    def __init__(self, host, port, db, password):
        # 优化Redis连接池配置 - 兼容Python 2.7
        self.cache_pool = redis.ConnectionPool(
            host=host,
            port=port,
            db=db,
            password=password,
            max_connections=50,         # 适中的连接数
            socket_timeout=5,           # socket超时
            socket_connect_timeout=5,   # 连接超时
        )
        self.cache_redis = redis.Redis(connection_pool=self.cache_pool)


    def set(self, key, value, expiration=60*60*24*30):

        value = zlib.compress(pickle.dumps(value))
        self.cache_redis.set(key, value)
        self.cache_redis.expire(key, expiration)

    def get(self, key, default=None):
        res = self.cache_redis.get(key)
        if res:
            res = pickle.loads(zlib.decompress(res))
        elif res is None:
            res = default
        return res

    def delete(self, key):

        return self.cache_redis.delete(key)
    
    def getset(self, key, value, expiration=2):
        res = self.cache_redis.getset(key, value)
        self.cache_redis.expire(key, expiration)
        return res

    def flushdb(self):
        return self.cache_redis.flushdb()


def get_cache(s):

    host = s[0]
    port = s[1]
    db = s[2]
    password = s[3]

    return RedisCache(host,port,db,password)
