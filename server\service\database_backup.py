#-*- coding: utf-8 -*-
import datetime
from sqlalchemy import *
from sqlalchemy.databases import mysql
#from django.conf import settings
import sys,os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../', 'trunk/llol/src'))
import settings

#-------------------------------------------------------------------------------
DB_ECHO = settings.DB_ECHO
DB_ENCODING = 'utf-8'

DBENGINE = 'mysql://%s:%s@%s:%s/%s?charset=utf8' % settings.DB_ACCOUNT_PASSWORD['shards']['1']['master']
# 优化数据库备份连接配置 - 兼容MySQL 5.6.5
DB = create_engine(
    DBENGINE,
    echo=DB_ECHO,
    encoding=DB_ENCODING,
    pool_size=5,            # 备份专用连接数
    max_overflow=10,        # 最大溢出连接
    pool_recycle=3600,      # 连接回收时间
    pool_timeout=60,        # 备份操作超时时间更长
    connect_args={
        'connect_timeout': 15,
        'charset': 'utf8',
    }
)
meta = MetaData(DB)
#User
User_table = Table('user', meta,
    Column('uid', String(64), primary_key = True, nullable = False),	#用户id
    Column('zone', String(8), primary_key = True, nullable = False),	#分区id
    Column('user_data', BLOB),
    mysql_engine='InnoDB',
)

#fight_log
FightLog_table = Table('fight_log', meta,
    Column('uid', String(64), primary_key = True, nullable = False),	#用户id
    Column('zone', String(8), primary_key = True, nullable = False),	#分区id
    Column('log_data', BLOB),
    mysql_engine='InnoDB',
)

user_table_dict = {
        'user': User_table,
        }
fight_log_table_dict = {
        'fight_log': FightLog_table,
        }
for item in range(settings.USER_TABLE_NUM):
    #User
    user_table_dict['user_%s' % item] = Table('user_%s' % item, meta,
        Column('uid', String(64), primary_key = True, nullable = False),	#用户id
        Column('zone', String(8), primary_key = True, nullable = False),	#分区id
        Column('user_data', BLOB),
        mysql_engine='InnoDB',
    )

    #fight_log
    fight_log_table_dict['fight_log_%s' % item] = Table('fight_log_%s' % item, meta,
        Column('uid', String(64), primary_key = True, nullable = False),	#用户id
        Column('zone', String(8), primary_key = True, nullable = False),	#分区id
        Column('log_data', BLOB),
        mysql_engine='InnoDB',
    )


#country_club
CountryClub_table = Table('country_club', meta,
    Column('zone', String(8), primary_key = True, nullable = False),	#分区id
    Column('club_data', BLOB),
    mysql_engine='InnoDB',
)

#world
World_table = Table('world', meta,
    Column('zone', String(8), primary_key = True, nullable = False),	#分区id
    Column('world_data', BLOB),
    mysql_engine='InnoDB',
)

#system_data
System_data_table = Table('system_data', meta,
    Column('zone', String(8), primary_key = True, nullable = False),	#分区id
    Column('data', BLOB),
    mysql_engine='InnoDB',
)

#pk_robot
Pk_robot_table = Table('pk_robot', meta,
    Column('zone', String(8), primary_key = True, nullable = False),	#分区id
    Column('robot_data', BLOB),
    mysql_engine='InnoDB',
)

#跨服战
#此表结构在game_lib.db.database中有复制，如修改须同步
UserDuplicate_table = Table('user_duplicate', meta,
    Column('udid', String(32), primary_key = True, nullable = False),
    Column('uid', String(16), index = True, nullable = False),
    Column('pf', String(64)),
    Column('zone', String(8), index=True, nullable = False),
    Column('group_id', String(16), index = True, nullable = False),
    Column('uname', String(64), nullable = False),
    Column('head', String(64), nullable = False),
    Column('level', Integer, index=True, nullable = False),
    Column('prestige', Integer, index=True, nullable = False),
    Column('join_time', DateTime, index=True),
    Column('open_date', Integer, index=True, nullable = False),
    Column('hero_data', BLOB), 
    Column('troop_data', BLOB), 
    Column('power', Integer, nullable = False),
    Column('pay_money', Integer, nullable = False), ## 截止报名时的充值总额
    Column('admin_pay', Integer, nullable = False), ## 截止报名时的虚拟充值总额
    Column('dnum', Integer, nullable = False), ## 跨服server序号
    mysql_engine='InnoDB',
)











