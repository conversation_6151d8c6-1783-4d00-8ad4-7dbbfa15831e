#!/bin/bash

echo "========== 磁盘使用分析 =========="

# 检查磁盘使用情况
echo "1. 总体磁盘使用情况:"
df -h

echo ""
echo "2. 查找大文件 (>100MB):"
find /data -type f -size +100M -exec ls -lh {} \; 2>/dev/null | head -20

echo ""
echo "3. 查找日志文件大小:"
echo "游戏日志:"
du -sh /data/logs/* 2>/dev/null | sort -hr | head -10

echo ""
echo "数据库日志:"
du -sh /var/log/mysql* 2>/dev/null

echo ""
echo "4. 可清理的内容建议:"
echo "- 旧日志文件 (超过30天)"
echo "- 临时文件"
echo "- 数据库备份文件"
echo "- 游戏备份文件"

echo ""
echo "5. 安全清理命令 (请谨慎执行):"
echo "# 清理30天前的日志"
echo "find /data/logs -name '*.log' -mtime +30 -delete"
echo ""
echo "# 清理临时文件"
echo "find /tmp -type f -mtime +7 -delete"
echo ""
echo "# 清理旧的备份文件"
echo "find /data -name '*.backup' -mtime +7 -delete"

echo ""
echo "========== 分析完成 =========="
