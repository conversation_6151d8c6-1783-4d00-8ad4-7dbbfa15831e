#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
跨服战报名调试脚本
用于测试和排查跨服战报名问题
"""

import sys
import os
import datetime
import json

# 添加路径
sys.path.insert(0, '/data/server/service')
sys.path.insert(0, '/data/server/trunk/llol/src')

def test_duplicate_join():
    """测试跨服战报名功能"""
    print("=" * 50)
    print("跨服战报名调试测试")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from server import User, game_config
        import settings
        
        print("1. 检查跨服战配置...")
        
        # 检查跨服战配置
        if hasattr(game_config, 'duplicate'):
            print("✓ 跨服战配置存在")
            print("跨服战开关:", game_config.duplicate.get('switch', 'N/A'))
            print("跨服战段位配置数量:", len(game_config.duplicate.get('level', [])))
        else:
            print("✗ 跨服战配置不存在")
            return
        
        print("\n2. 检查跨服战时间配置...")
        
        # 检查时间配置
        if hasattr(User, 'duplicate_time'):
            print("✓ User.duplicate_time 存在:", User.duplicate_time)
        else:
            print("✗ User.duplicate_time 不存在")
        
        print("\n3. 测试时间检查函数...")
        
        # 测试时间检查
        try:
            now = User.check_season_num()
            print("✓ 当前时间:", now)
            print("✓ check_season_num 函数正常")
        except Exception as e:
            print("✗ check_season_num 函数错误:", str(e))
        
        print("\n4. 测试跨服战报名函数...")
        
        # 创建一个测试用户
        test_uid = 10000001  # 使用一个测试UID
        test_level = 0  # 青铜段位
        
        print("测试UID:", test_uid)
        print("测试段位:", test_level)
        
        # 检查用户是否存在
        if test_uid in User.users:
            user = User.users[test_uid]
            print("✓ 找到测试用户")
            
            # 测试admin_join_duplicate函数
            try:
                # 获取用户最强的5个英雄
                hero_list = []
                for hid, hero in user.hero.items():
                    if hero.get('star', 0) > 0:  # 只选择已激活的英雄
                        power = hero.get('power', 0)
                        hero_list.append((hid, power))
                
                # 按战力排序，取前5个
                hero_list.sort(key=lambda x: x[1], reverse=True)
                top_5_heroes = [h[0] for h in hero_list[:5]]
                
                print("用户最强5个英雄:", top_5_heroes)
                
                if len(top_5_heroes) >= 5:
                    print("✓ 用户有足够的英雄参加跨服战")
                    
                    # 调用报名函数
                    result = user.admin_join_duplicate(top_5_heroes, test_level)
                    print("✓ 报名函数调用成功")
                    print("报名结果:", result)
                else:
                    print("✗ 用户英雄数量不足")
                    
            except Exception as e:
                print("✗ 报名函数调用失败:", str(e))
                import traceback
                traceback.print_exc()
        else:
            print("✗ 测试用户不存在，请使用实际存在的用户UID")
            print("当前在线用户数量:", len(User.users))
            if User.users:
                print("示例用户UID:", list(User.users.keys())[:5])
        
    except ImportError as e:
        print("✗ 导入模块失败:", str(e))
        print("请确保脚本在正确的目录下运行")
    except Exception as e:
        print("✗ 测试过程中发生错误:", str(e))
        import traceback
        traceback.print_exc()

def check_duplicate_config():
    """检查跨服战相关配置"""
    print("\n" + "=" * 50)
    print("跨服战配置检查")
    print("=" * 50)
    
    try:
        sys.path.insert(0, '/data/server/trunk/llol/src')
        from game_lib.logics import game_config
        
        print("1. 检查跨服战基础配置...")
        if hasattr(game_config, 'duplicate'):
            duplicate_config = game_config.duplicate
            print("✓ 跨服战配置存在")
            print("开关状态:", duplicate_config.get('switch', 'N/A'))
            print("段位配置:", len(duplicate_config.get('level', [])))
            print("报名时间配置:", duplicate_config.get('join_time', 'N/A'))
            
            # 检查段位配置
            levels = duplicate_config.get('level', [])
            for i, level in enumerate(levels):
                if len(level) > 5:
                    print(f"段位{i} ({game_config.return_msg.get(level[5], 'Unknown')}):", level)
                else:
                    print(f"段位{i} 配置不完整:", level)
        else:
            print("✗ 跨服战配置不存在")
            
    except Exception as e:
        print("✗ 配置检查失败:", str(e))
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("跨服战报名问题调试脚本")
    print("请确保在服务器上运行此脚本")
    print()
    
    # 检查配置
    check_duplicate_config()
    
    # 测试报名功能
    test_duplicate_join()
    
    print("\n" + "=" * 50)
    print("调试完成")
    print("=" * 50)
