global.window = global;
window.document = null;
var request = require('request');
const http = require('http');
var url = require('url');
require('./LayaSample.max')

var port = parseInt(process.argv[2])
if(isNaN(port)) port=3000;

// 优化Node.js性能配置
process.env.UV_THREADPOOL_SIZE = 128;  // 增加线程池大小
process.setMaxListeners(0);            // 移除事件监听器限制

console.log('listen on '+port)
console.log('UV_THREADPOOL_SIZE:', process.env.UV_THREADPOOL_SIZE)
var api_url = 'http://118.178.139.38:8500/gateway/';
var no_try = 1;


request.post(
    api_url, {json: {"id":"33","method":"sys.get_config","params":{'pf': 'local'},"cfg_version":"","app_version":"1.0.0"}},

    function(error, response, body){
        //console.log('============');
        //console.log(typeof body);
        //console.log(body);
        //console.log('============');
        sg.cfg.ConfigServer.formatTo(body.data);
    
        //var ls = new LayaSample();
        //ls.testPrepare()
        
        //var a = new Date();
        //sg.fight.test.TestFightData.getRandomFightData();
        //ls.testPrepareTime();
        //var b = new Date();

        //var a = new Date();
        //console.log(sg.fight.logic.utils.FightInterface.doFight(sg.fight.test.TestFightData.getFightInitData()));
        //var b = new Date();
        //console.log(b-a);

        // console.log('starting server...')
        version = body.data.cfg_version;

        // 优化HTTP服务器配置
        var server = http.createServer(function(req,res){
            // 设置响应头优化
            res.setHeader('Content-Type', 'application/json; charset=utf-8');
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

            // 处理OPTIONS预检请求
            if (req.method === 'OPTIONS') {
                res.writeHead(200);
                res.end();
                return;
            }
            var post='';      
            req.on('data',function(chunk){  
                post += chunk;  
            }); 
            var pathName = url.parse(req.url).pathname;

            // console.log('============');
            // console.log(pathName);

            req.on('end',function(){  
                try{
                    post=JSON.parse(post);  
                }catch(error){
                    res.end("Error: "+error)
                    console.log("Error: "+error)
                    return;
                }

                //console.log(post);
                //console.log(typeof post)
                //console.log('============');

                //var data = '';
                //var a = new Date();
                //for(var i=0; i<1000; i++){
                    //data = new sg.model.ModelPrepare(post, true).getData();
                //
                if(no_try==1){

                    if(pathName=='/data/')
                        data = sg.model.ModelPrepare.initPrepareDataArr(post, true);
                    else if(pathName=='/data2/')
                        data = sg.model.ModelPrepare.initPrepareDataArr(post, false);
                    else if(pathName=='/doFight/')
                        data = sg.fight.logic.utils.FightInterface.doFight(post);
                    else if(pathName=='/testFight/')
                        data = sg.fight.logic.utils.FightInterface.testBattleJSON(post, 0);
                    else if(pathName=='/testFightFormat/')
                        data = sg.fight.logic.utils.FightInterface.testBattleFormatJSON(post);
                    else if(pathName=='/getHeroesPower/')
                        data = sg.model.ModelPrepare.getHeroesPower(post);
                    else if(pathName=='/getHeroesData/')
                        data = sg.model.ModelPrepare.getHeroesDataArr(post);
                    else if(pathName=='/getHeroesData2/')
                        //获取若干英雄（数组内为对象）的最终四维数据，type传3
                        data = sg.model.ModelPrepare.getPrepareDataArr(post, 3);
                    else if(pathName=='/sync_configs/'){
                        version = post.version;
                        sg.cfg.ConfigServer.formatTo({'cfg': post});
                        data = {};
                    }else if(pathName=='/get_version/'){
                        data = version;
                    } else if(pathName=='/doBattle/')
                        data = sg.fight.logic.utils.FightInterface.doBattle(post);
                    else if(pathName=='/getVersion/')
                        // 获取js代码版本
                        data = sg.fight.logic.utils.FightInterface.getVersion();
                    else if(pathName=='/getAllPower/')
                        data = sg.model.ModelPrepare.getAllPower(post);
                    else
                        data = null;

                }else{

                    try{
                        if(pathName=='/data/')
                            data = sg.model.ModelPrepare.initPrepareDataArr(post, true);
                        else if(pathName=='/data2/')
                            data = sg.model.ModelPrepare.initPrepareDataArr(post, false);
                        else if(pathName=='/doFight/')
                            data = sg.fight.logic.utils.FightInterface.doFight(post);
                        else if(pathName=='/testFight/')
                            data = sg.fight.logic.utils.FightInterface.testBattleJSON(post, 1);
                        else if(pathName=='/testFightFormat/')
                            data = sg.fight.logic.utils.FightInterface.testBattleFormatJSON(post);
                        else if(pathName=='/getHeroesPower/')
                            data = sg.model.ModelPrepare.getHeroesPower(post);
                        else if(pathName=='/getHeroesData/')
                            data = sg.model.ModelPrepare.getHeroesDataArr(post);
                        else if(pathName=='/getHeroesData2/')
                            //获取若干英雄（数组内为对象）的最终四维数据，type传3
                            data = sg.model.ModelPrepare.getPrepareDataArr(post, 3);
                        else if(pathName=='/sync_configs/'){
                            version = post.version;
                            sg.cfg.ConfigServer.formatTo({'cfg': post});
                            data = {};
                        }else if(pathName=='/get_version/'){
                            data = version;
                        } else if(pathName=='/doBattle/')
                            data = sg.fight.logic.utils.FightInterface.doBattle(post);
                        else if(pathName=='/getVersion/')
                            data = sg.fight.logic.utils.FightInterface.getVersion();
                        else if(pathName=='/getAllPower/')
                            data = sg.model.ModelPrepare.getAllPower(post);
                        else
                            data = null;
                    }catch(error){
                        console.log("Error: "+error)
                        data = null;
                    }

                }

                

                //}

                //var b = new Date();

                //console.log('=============', b-a);
                
                res.end(JSON.stringify(data));  
            });  

        });

        // 优化服务器监听配置
        server.listen(port, function() {
            console.log('战斗服务器启动成功，端口:', port);
            console.log('进程ID:', process.pid);
            console.log('内存使用:', process.memoryUsage());
        });

        // 设置服务器超时
        server.timeout = 30000;  // 30秒超时
        server.keepAliveTimeout = 5000;  // 5秒保持连接
        server.headersTimeout = 6000;    // 6秒头部超时

        // 优雅关闭处理
        process.on('SIGTERM', function() {
            console.log('收到SIGTERM信号，正在关闭服务器...');
            server.close(function() {
                console.log('服务器已关闭');
                process.exit(0);
            });
        });

        process.on('SIGINT', function() {
            console.log('收到SIGINT信号，正在关闭服务器...');
            server.close(function() {
                console.log('服务器已关闭');
                process.exit(0);
            });
        });

    }

)

