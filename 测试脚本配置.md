# 脚本配置测试说明

## ✅ **已修复的问题**

### **1. 移除过时的硬编码服务**
- ❌ 删除 `Zone_2` (端口5002) - 不存在的区服
- ❌ 删除 `Fight_3002` (端口3002) - 改为单战斗服务器

### **2. 动态服务配置**
```bash
# 新的动态配置
declare -A services=(
    ["Analytics_7701"]="7701"
    ["Backend_8500"]="8500"
    ["Fight_3001"]="3001"        # 单战斗服务器
    ["Backup_SG3"]="0"
)

# 动态添加配置的区服
for zone_id in "${!ZONE_CONFIG[@]}"; do
    services["Zone_${zone_id}"]="${ZONE_CONFIG[$zone_id]}"
done
```

### **3. 智能端口清理**
```bash
# 动态处理区服端口
Zone_*)
    local zone_id="${name#Zone_}"
    if [[ -n "${ZONE_CONFIG[$zone_id]}" ]]; then
        port="${ZONE_CONFIG[$zone_id]}"
    fi
    ;;
```

## 🎯 **当前正式服配置**

### **区服配置**
```bash
ZONE_CONFIG[1]="5001"    # 1区 - 端口5001
ZONE_CONFIG[4]="5004"    # 4区 - 端口5004  
ZONE_CONFIG[5]="5005"    # 5区 - 端口5005
# ZONE_CONFIG[6]="5006"  # 6区 - 端口5006 (预留)
```

### **服务列表 (自动生成)**
- Analytics_7701 → 端口7701
- Backend_8500 → 端口8500
- Fight_3001 → 端口3001 (单进程128线程)
- Zone_1 → 端口5001 (动态添加)
- Zone_4 → 端口5004 (动态添加)
- Zone_5 → 端口5005 (动态添加)
- Backup_SG3 → 端口0 (特殊处理)

## 🚀 **添加新区服方法**

### **添加6区服务器**
```bash
# 1. 编辑 start.sh 文件
vim start.sh

# 2. 取消注释6区配置
ZONE_CONFIG[6]="5006"  # 6区 - 端口5006

# 3. 重启脚本
./start.sh restart
```

### **自动效果**
- ✅ `show_status` 自动显示Zone_6
- ✅ `restart-zone 6` 自动支持
- ✅ 端口清理自动处理5006端口
- ✅ 帮助信息自动更新

## 📋 **测试命令**

### **查看当前配置**
```bash
./start.sh status
```

### **测试区服重启**
```bash
./start.sh restart-zone 1    # 测试1区
./start.sh restart-zone 4    # 测试4区
./start.sh restart-zone 5    # 测试5区
```

### **测试战斗服重启**
```bash
./start.sh restart-fight 3001  # 单战斗服务器
```

## 🔧 **配置验证**

### **检查动态服务列表**
脚本启动时会显示：
```
正式服区服配置：
  1区 - 端口5001
  4区 - 端口5004
  5区 - 端口5005
```

### **检查服务状态**
```
服务名称             状态       PID      端口
----------------------------------------
Analytics_7701       运行       12345    7701
Backend_8500         运行       12346    8500
Fight_3001           运行       12347    3001
Zone_1               运行       12348    5001
Zone_4               运行       12349    5004
Zone_5               运行       12350    5005
Backup_SG3           运行       12351    0
```

## ✅ **优势总结**

1. **无硬编码** - 所有配置基于 ZONE_CONFIG 数组
2. **自动扩展** - 添加新区服只需修改一行
3. **智能清理** - 端口清理自动适配新区服
4. **一致性** - 所有功能使用相同的配置源
5. **维护简单** - 单一配置点，避免遗漏更新
