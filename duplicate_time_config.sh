#!/bin/bash
# 跨服战时间调整工具

echo "=========================================="
echo "      跨服战自动化时间调整工具"
echo "=========================================="
echo ""

# 显示当前配置
show_current_config() {
    echo "📅 当前自动化时间配置："
    echo ""
    
    if [ ! -f "/data/server/service/duplicate_auto.sh" ]; then
        echo "❌ 未找到自动化脚本，请先运行安装程序"
        return 1
    fi
    
    echo "  周一 09:00 - 启动管理服务器（为周三做准备）"
    echo "  周三 17:00 - 启动战斗服务器"
    echo "  周三 23:00 - 停止战斗服务器"
    echo "  周四 09:00 - 重启管理服务器（为周六做准备）"
    echo "  周六 17:00 - 启动战斗服务器"
    echo "  周日 01:00 - 停止所有服务器"
    echo ""
    
    # 从脚本中提取实际配置
    echo "📋 脚本中的实际配置："
    grep -A 1 '"[0-9]-[0-9][0-9]")' /data/server/service/duplicate_auto.sh | grep -E '"[0-9]-[0-9][0-9]"' | while read line; do
        day_hour=$(echo "$line" | grep -o '"[0-9]-[0-9][0-9]"' | tr -d '"')
        day=$(echo "$day_hour" | cut -d'-' -f1)
        hour=$(echo "$day_hour" | cut -d'-' -f2)
        
        case $day in
            1) day_name="周一" ;;
            2) day_name="周二" ;;
            3) day_name="周三" ;;
            4) day_name="周四" ;;
            5) day_name="周五" ;;
            6) day_name="周六" ;;
            7) day_name="周日" ;;
        esac
        
        comment=$(echo "$line" | grep -o '#.*' | sed 's/# *//')
        echo "  $day_name ${hour}:00 - $comment"
    done
    echo ""
}

# 预设时间方案
preset_schedules() {
    echo "🕐 预设时间方案："
    echo ""
    echo "1. 默认方案（当前）"
    echo "   周一09:00启动管理 → 周三17:00启动战斗 → 周三23:00停止战斗"
    echo "   周四09:00重启管理 → 周六17:00启动战斗 → 周日01:00全部停止"
    echo ""
    echo "2. 晚间方案"
    echo "   周一20:00启动管理 → 周三19:00启动战斗 → 周四00:00停止战斗"
    echo "   周四20:00重启管理 → 周六19:00启动战斗 → 周日01:00全部停止"
    echo ""
    echo "3. 早间方案"
    echo "   周一08:00启动管理 → 周三14:00启动战斗 → 周三18:00停止战斗"
    echo "   周四08:00重启管理 → 周六14:00启动战斗 → 周日01:00全部停止"
    echo ""
    echo "4. 自定义方案"
    echo "   手动编辑配置文件"
    echo ""
}

# 应用预设方案
apply_preset() {
    if [ ! -f "/data/server/service/duplicate_auto.sh" ]; then
        echo "❌ 未找到自动化脚本，请先运行安装程序"
        return 1
    fi
    
    # 备份原文件
    cp /data/server/service/duplicate_auto.sh /data/server/service/duplicate_auto.sh.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ 已备份原配置文件"
    
    case "$1" in
        "2")
            echo "🌙 应用晚间方案..."
            sed -i 's/"1-09")/"1-20")/' /data/server/service/duplicate_auto.sh
            sed -i 's/"3-17")/"3-19")/' /data/server/service/duplicate_auto.sh
            sed -i 's/"3-23")/"4-00")/' /data/server/service/duplicate_auto.sh
            sed -i 's/"4-09")/"4-20")/' /data/server/service/duplicate_auto.sh
            sed -i 's/"6-17")/"6-19")/' /data/server/service/duplicate_auto.sh
            echo "✅ 晚间方案已应用"
            ;;
        "3")
            echo "🌅 应用早间方案..."
            sed -i 's/"1-09")/"1-08")/' /data/server/service/duplicate_auto.sh
            sed -i 's/"3-17")/"3-14")/' /data/server/service/duplicate_auto.sh
            sed -i 's/"3-23")/"3-18")/' /data/server/service/duplicate_auto.sh
            sed -i 's/"4-09")/"4-08")/' /data/server/service/duplicate_auto.sh
            sed -i 's/"6-17")/"6-14")/' /data/server/service/duplicate_auto.sh
            echo "✅ 早间方案已应用"
            ;;
        "1")
            echo "🔄 恢复默认方案..."
            echo "请重新运行安装脚本以恢复默认配置"
            echo "或者从备份文件恢复："
            echo "ls /data/server/service/duplicate_auto.sh.backup.*"
            ;;
        "4")
            echo "🛠️  进入自定义编辑模式..."
            echo "即将打开配置文件编辑器"
            echo "请找到 case 语句部分，修改时间点"
            echo "格式：\"星期-小时\") # 例如 \"3-19\" 表示周三19点"
            read -p "按回车键继续编辑..."
            nano /data/server/service/duplicate_auto.sh
            ;;
        *)
            echo "❌ 无效选择"
            return 1
            ;;
    esac
}

# 测试配置
test_config() {
    echo "🧪 测试当前配置..."
    if [ ! -f "/data/server/service/duplicate_auto.sh" ]; then
        echo "❌ 未找到自动化脚本"
        return 1
    fi
    
    # 检查脚本语法
    if bash -n /data/server/service/duplicate_auto.sh; then
        echo "✅ 脚本语法正确"
    else
        echo "❌ 脚本语法错误，请检查配置"
        return 1
    fi
    
    # 手动运行一次
    echo "🔄 手动执行一次脚本..."
    /data/server/service/duplicate_auto.sh
    
    echo ""
    echo "📝 最近的日志："
    if [ -f "/data/logs/duplicate_auto.log" ]; then
        tail -5 /data/logs/duplicate_auto.log
    else
        echo "暂无日志"
    fi
}

# 查看定时任务状态
check_crontab() {
    echo "⏰ 定时任务状态："
    if crontab -l 2>/dev/null | grep -q "duplicate_auto.sh"; then
        echo "✅ 定时任务已设置"
        crontab -l 2>/dev/null | grep "duplicate_auto.sh"
        echo ""
        echo "📅 下次执行时间："
        echo "每小时的0分执行一次检查"
    else
        echo "❌ 定时任务未设置"
        echo "请运行安装脚本设置定时任务"
    fi
}

# 主菜单
main_menu() {
    while true; do
        echo "请选择操作："
        echo "1. 查看当前时间配置"
        echo "2. 查看预设方案"
        echo "3. 应用预设方案"
        echo "4. 测试当前配置"
        echo "5. 查看定时任务状态"
        echo "6. 查看日志"
        echo "7. 退出"
        echo ""
        read -p "请输入选择 (1-7): " choice
        
        case $choice in
            1)
                show_current_config
                ;;
            2)
                preset_schedules
                ;;
            3)
                preset_schedules
                read -p "请选择方案 (1-4): " preset
                apply_preset "$preset"
                ;;
            4)
                test_config
                ;;
            5)
                check_crontab
                ;;
            6)
                echo "📝 自动化日志："
                if [ -f "/data/logs/duplicate_auto.log" ]; then
                    tail -20 /data/logs/duplicate_auto.log
                else
                    echo "暂无自动化日志"
                fi
                echo ""
                echo "📝 手动操作日志："
                if [ -f "/data/logs/duplicate_manual.log" ]; then
                    tail -10 /data/logs/duplicate_manual.log
                else
                    echo "暂无手动操作日志"
                fi
                ;;
            7)
                echo "👋 再见！"
                exit 0
                ;;
            *)
                echo "❌ 无效选择，请重新输入"
                ;;
        esac
        echo ""
        read -p "按回车键继续..."
        echo ""
    done
}

# 检查是否有参数
if [ $# -eq 0 ]; then
    main_menu
else
    case "$1" in
        "show")
            show_current_config
            ;;
        "test")
            test_config
            ;;
        "cron")
            check_crontab
            ;;
        *)
            echo "用法: $0 [show|test|cron]"
            echo "或直接运行进入交互模式"
            ;;
    esac
fi
